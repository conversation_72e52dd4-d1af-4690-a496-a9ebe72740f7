const mongoose = require('mongoose');
const UsersProfileAnalysisPrompts = require('./models/users-profile-analysis-prompts');

// Connect to your local database - UPDATE THIS CONNECTION STRING
mongoose.connect('mongodb://localhost:27017/your_database_name', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// This is the exact logic from getpromptResponseForProfileAnalysis
function processOpenAIResponse(rawOutput, promptsLength = 0, locale = 'en') {
  let formattedOutputResults = []; // Note: starts as array, just like in the original function
  let isError = false;
  let errorMessage = null;
  
  console.log('🔄 Processing OpenAI response...\n');
  console.log('📝 Raw output (first 500 chars):', rawOutput.substring(0, 500) + '...\n');
  
  if (rawOutput) {
    try {
      // Exact same cleaning logic
      const parsedOutput = JSON.parse(rawOutput.replace('```json', '').replace('```', '').replace(/\n/g, '').trim());
      console.log('✅ JSON parsing successful');
      console.log('📊 Parsed output type:', typeof parsedOutput);
      console.log('📊 Parsed output keys:', Object.keys(parsedOutput).join(', '));
      
      if (parsedOutput?.output && typeof parsedOutput.output === 'object') {
        console.log('✅ parsedOutput.output exists and is object');
        console.log('📊 parsedOutput.output keys:', Object.keys(parsedOutput.output).join(', '));
        
        // THE CRITICAL CONDITION - this is where the issue likely occurs
        if (parsedOutput.output.insights_overall && 
            parsedOutput.output.insights_overall.insights_biography && 
            parsedOutput.output.insights_overall.insights_photos &&
            parsedOutput.output.insights_overall.insights_prompts) {
          
          console.log('✅ NESTED STRUCTURE CONDITION MET');
          const nestedData = parsedOutput.output.insights_overall;
          formattedOutputResults = {
            insights_overall: { overall: nestedData.overall },
            insights_biography: nestedData.insights_biography,
            insights_photos: nestedData.insights_photos,
            insights_prompts: nestedData.insights_prompts
          };
          console.log('📦 Using nested structure, formattedOutputResults keys:', Object.keys(formattedOutputResults).join(', '));
          
        } else {
          console.log('❌ NESTED STRUCTURE CONDITION NOT MET');
          console.log('🔍 Detailed condition check:');
          console.log('- insights_overall exists:', !!parsedOutput.output.insights_overall);
          if (parsedOutput.output.insights_overall) {
            console.log('- insights_overall keys:', Object.keys(parsedOutput.output.insights_overall).join(', '));
            console.log('- insights_biography exists:', !!parsedOutput.output.insights_overall.insights_biography);
            console.log('- insights_photos exists:', !!parsedOutput.output.insights_overall.insights_photos);
            console.log('- insights_prompts exists:', !!parsedOutput.output.insights_overall.insights_prompts);
          }
          
          formattedOutputResults = { ...parsedOutput.output };
          console.log('📦 Using spread operator, formattedOutputResults keys:', Object.keys(formattedOutputResults).join(', '));
        }
        
        // Cleanup operations (exact same as original)
        if(formattedOutputResults?.insights_overall?.items) {
          console.log('🗑️  Deleting insights_overall.items');
          delete formattedOutputResults.insights_overall.items;
        }
        if(formattedOutputResults?.insights_biography?.items) {
          console.log('🗑️  Deleting insights_biography.items');
          delete formattedOutputResults.insights_biography.items;
        }
        if(formattedOutputResults?.insights_prompts?.items?.length) {
          console.log(`🔪 Slicing insights_prompts.items to ${promptsLength} items`);
          formattedOutputResults.insights_prompts.items = formattedOutputResults.insights_prompts.items.slice(0, promptsLength);
        } else if(formattedOutputResults.insights_prompts) {
          console.log('📝 Setting insights_prompts.items to empty array');
          formattedOutputResults.insights_prompts.items = [];
        }
        
      } else {
        console.log('❌ parsedOutput.output does not exist or is not an object');
        if (parsedOutput.output) {
          console.log('📊 parsedOutput.output type:', typeof parsedOutput.output);
          console.log('📊 parsedOutput.output value:', parsedOutput.output);
        } else {
          console.log('📊 parsedOutput.output is undefined/null');
        }
      }
      
    } catch (err) {
      console.log("❌ Error parsing JSON or processing results:", err.message);
      console.log("🔍 Error details:", err);
      isError = true;
      errorMessage = `JSON parsing error: ${err.message}`;
    }
  }
  
  console.log('\n📋 FINAL PROCESSING RESULTS:');
  console.log('- formattedOutputResults type:', typeof formattedOutputResults);
  console.log('- formattedOutputResults is array:', Array.isArray(formattedOutputResults));
  console.log('- formattedOutputResults keys/length:', Array.isArray(formattedOutputResults) ? 
    `Array with ${formattedOutputResults.length} items` : 
    `Object with keys: ${Object.keys(formattedOutputResults).join(', ')}`);
  console.log('- isEmpty:', Array.isArray(formattedOutputResults) ? 
    formattedOutputResults.length === 0 : 
    Object.keys(formattedOutputResults).length === 0);
  console.log('- isError:', isError);
  console.log('- errorMessage:', errorMessage);
  
  return {
    formattedOutputResults,
    isError,
    errorMessage
  };
}

async function processFailedDocument() {
  try {
    console.log('🔍 Finding a failed document to process...\n');
    
    // Find a failed document
    const failedDoc = await UsersProfileAnalysisPrompts.findOne({
      isError: false,
      output: { $exists: true, $ne: null },
      $or: [
        { outputResults: { $exists: false } },
        { outputResults: null },
        { outputResults: {} },
        { outputResults: [] }
      ]
    }).sort({ createdAt: -1 });
    
    if (!failedDoc) {
      console.log('❌ No failed document found.');
      return;
    }
    
    console.log('✅ Found failed document');
    console.log(`ID: ${failedDoc._id}`);
    console.log(`Created: ${failedDoc.createdAt}`);
    console.log(`Current outputResults: ${JSON.stringify(failedDoc.outputResults)}\n`);
    
    // Extract the raw OpenAI response
    let rawOpenAIResponse;
    try {
      const outputData = JSON.parse(failedDoc.output);
      rawOpenAIResponse = outputData.output;
      console.log('📦 Extracted OpenAI response from parsed output field');
    } catch (e) {
      rawOpenAIResponse = failedDoc.output;
      console.log('📦 Using raw output field as OpenAI response');
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('PROCESSING THROUGH getpromptResponseForProfileAnalysis LOGIC');
    console.log('='.repeat(80));
    
    // Process it through our exact replica of the function logic
    const result = processOpenAIResponse(rawOpenAIResponse, 0, 'en');
    
    console.log('\n' + '='.repeat(80));
    console.log('COMPARISON WITH STORED RESULT');
    console.log('='.repeat(80));
    console.log('Stored outputResults:', JSON.stringify(failedDoc.outputResults));
    console.log('Processed outputResults:', JSON.stringify(result.formattedOutputResults));
    console.log('Match:', JSON.stringify(failedDoc.outputResults) === JSON.stringify(result.formattedOutputResults));
    
    if (Array.isArray(result.formattedOutputResults) && result.formattedOutputResults.length === 0) {
      console.log('\n🚨 ISSUE IDENTIFIED: formattedOutputResults is an empty array!');
      console.log('This explains why outputResults is empty in the database.');
      console.log('The issue is likely in the JSON structure not matching the expected nested format.');
    } else if (typeof result.formattedOutputResults === 'object' && Object.keys(result.formattedOutputResults).length === 0) {
      console.log('\n🚨 ISSUE IDENTIFIED: formattedOutputResults is an empty object!');
      console.log('This explains why outputResults is empty in the database.');
    }
    
  } catch (error) {
    console.error('❌ Error during processing:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the processing
processFailedDocument();
