/* eslint-disable indent */
const HiveService = require('./hive');
const RekognitionService = require('./rekognition');
const { cloudwatch } = require('../cloudwatch');
const ImageModeration = require('../../models/image-moderation');
const constants = require('../constants');

class ImageModerationService {
  constructor(configuredServices = ['Rekognition']) {
    this.services = configuredServices.map((serviceName) => {
      switch (serviceName) {
        case 'Rekognition':
          return { name: serviceName, instance: new RekognitionService() };
        case 'Hive':
          return { name: serviceName, instance: new HiveService() };
        default:
          throw new Error(`Unsupported moderation service: ${serviceName}`);
      }
    });
  }

  // isVerification determines if the moderation is for a verification image and it'll only check for overlay text
  async moderatePicture(key, isVideoThumbnail, isVerification) {
    const moderationResults = {};

    try {
      const results = await Promise.allSettled(this.services.map((service) => service.instance.moderatePicture(key, isVideoThumbnail)));

      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const { name: serviceName } = this.services[i];

        const name = serviceName.toLowerCase() === 'hive' ? 'hive_v2' : serviceName.toLowerCase();
        if (result.status === 'fulfilled') {
          if (isVerification && name === 'hive_v2') {
            const overlayText = result.value?.detectionLabels?.find((label) => label.Name === 'yes_overlay_text');
            moderationResults[name] = overlayText ? overlayText.Name : false;
            result.value.isFlagged = !!overlayText;
            result.value.flaggedModerationLabel = overlayText;
          } else {
            moderationResults[name] = result.value?.flaggedModerationLabel?.Name || false;
          }
          if (result.value?.detectionLabels?.some((label) => ['yes_overlay_text', 'text'].includes(label?.Name))) {
            moderationResults.text_detected = true;
          }
          await this.createRecord(key, isVideoThumbnail, result.value, serviceName);
        } else {
          console.log(`ThirdPartyError ${serviceName}:`, result.reason);
          await this.createErrorRecord(key, result.reason?.message, serviceName);
          await this.reportErrorToCloudWatch(serviceName);
          moderationResults[name] = false;
        }
      }
    } catch (err) {
      console.log('Unexpected Error in moderatePicture:', err);
    }

    return moderationResults;
  }

  async createRecord(key, isVideoThumbnail, data, serviceName) {
    await ImageModeration.create({
      key,
      url: `${constants.IMAGE_DOMAIN}${key}`,
      moderationLabels: data.detectionLabels,
      isVideoThumbnail,
      isFlagged: data.isFlagged,
      flaggedModerationLabel: data.flaggedModerationLabel || undefined,
      serviceName,
    });
  }

  async createErrorRecord(key, errorMessage, serviceName) {
    await ImageModeration.create({
      key,
      url: `${constants.IMAGE_DOMAIN}${key}`,
      isError: true,
      errorMessage,
      serviceName,
    });
  }

  async reportErrorToCloudWatch(serviceName) {
    const params = {
      MetricData: [
        {
          MetricName: `${serviceName}Errors`,
          Value: 1,
        },
      ],
      Namespace: `ThirdPartyMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();
  }
}

module.exports = { ImageModerationService, rekognitionModerationInstance: new ImageModerationService() };
