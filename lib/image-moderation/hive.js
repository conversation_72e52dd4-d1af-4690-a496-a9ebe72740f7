const constants = require('../constants');
const superagent = require('superagent');

const bannedClasses = {
  general_nsfw: 0.8,
  yes_bulge: 0.8,
  very_bloody: 0.8,
  hanging: 0.8,
  noose: 0.8,
  yes_emaciated_body: 0.8,
  yes_self_harm: 0.8,
  yes_nazi: 0.8,
  yes_kkk: 0.8,
  yes_sex_toy: 0.8,
  yes_sexual_intent: 0.8,
  yes_undressed: 0.75,
  yes_qr_code: 0.75,
};

class HiveService {
  /**
   * Original method - moderates using S3 key
   * @param {string} key - S3 object key
   */
  async moderatePicture(key) {
    const imageUrl = `${constants.IMAGE_DOMAIN}${key}`;
    return this._moderateImage(imageUrl);
  }

  /**
   * NEW public method - moderates using direct URL
   * @param {string} imageUrl - Full image URL
   */
  async moderatePictureByUrl(imageUrl) {
    this._validateImageUrl(imageUrl);
    return this._moderateImage(imageUrl);
  }

  /**
   * Validate image URL format and security
   * @param {string} url - URL to validate
   * @private
   */
  _validateImageUrl(url) {
    try {
      const parsed = new URL(url);
      
      // Validate protocol
      if (!['https:', 'http:'].includes(parsed.protocol)) {
        throw new Error(`Invalid URL protocol: ${parsed.protocol}. Only HTTP/HTTPS allowed`);
      }

      // // Basic path validation
      // if (!parsed.pathname || !parsed.pathname.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
      //   throw new Error('URL must point to an image file (jpg/png/gif/webp)');
      // }
    } catch (err) {
      throw new Error(`Invalid image URL: ${err.message}`);
    }
  }

  async _moderateImage(imageUrl) {
    const response = {
      isFlagged: false,
      detectionLabels: [],
    };

    const res = await superagent
      .post('https://api.thehive.ai/api/v2/task/sync')
      .set('authorization', `token ${process.env.HIVE_VISUAL_MODERATION_API_KEY}`)
      .field('url', imageUrl);

    for (const output of res.body.status[0].response.output) {
      for (const x of output.classes) {
        if (x.score >= 0.75 && !(x.class.includes('no_') || x.class.includes('not_'))) {
          const labelObject = {
            Confidence: x.score * 100,
            Name: x.class,
            ParentName: '',
          };

          response.detectionLabels.push(labelObject);

          if (bannedClasses[x.class] && x.score >= bannedClasses[x.class] && response.isFlagged === false) {
            response.isFlagged = true;
            response.flaggedModerationLabel = labelObject;
          }
        }
      }
    }
    return response;
  }
}

module.exports = HiveService;
