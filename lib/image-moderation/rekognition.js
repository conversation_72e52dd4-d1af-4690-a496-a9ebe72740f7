const s3 = require('../s3');
const { rekognition } = require('../rekognition');

const thumbBannedLabels = {
  'Exposed Male Genitalia': 96.6,
  'Exposed Female Nipple': 96.7,
  'Exposed Buttocks or Anus': 91,
};

const imageBannedLabels = {
  'Explicit Nudity': 91.8,
  'Exposed Male Genitalia': 91.25,
  'Exposed Female Genitalia': 91.25,
};

class RekognitionService {
  async moderatePicture(key, isVideoThumbnail) {
    const response = {
      isFlagged: false,
      detectionLabels: [],
    };

    const params = {
      Image: {
        S3Object: {
          Bucket: s3.AWS_S3_BUCKET,
          Name: key,
        },
      },
      MinConfidence: '75',
    };

    const data = await rekognition.detectModerationLabels(params).promise();
    response.detectionLabels = data.ModerationLabels;

    for (const label of data.ModerationLabels) {
      if (isVideoThumbnail && thumbBannedLabels[label.Name] && label.Confidence >= thumbBannedLabels[label.Name]) {
        response.isFlagged = true;
        response.flaggedModerationLabel = label;
        break;
      } else if (imageBannedLabels[label.Name] && label.Confidence >= imageBannedLabels[label.Name]) {
        response.isFlagged = true;
        response.flaggedModerationLabel = label;
        break;
      }
    }

    return response;
  }
}

module.exports = RekognitionService;
