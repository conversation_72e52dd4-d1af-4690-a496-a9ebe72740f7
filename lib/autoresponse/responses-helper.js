const Message = require('../../models/message');
const chatLib = require('../chat');
const messageLib = require('../message');
const { topics, issues, subIssues, templates, supportOptions } = require('./responses');
const { sendSocketEvent } = require('../socket');
const Chat = require('../../models/chat');
const TrackAutoreponseUsage = require('../../models/track-autoresponse-usage');
const { translate } = require('../translate');
const User = require('../../models/user');

const getTemplateText = (user, templateObj) => {
  if (user?.versionAtLeast('1.13.74') && templateObj?.v2_title) {
    return templateObj?.v2_title || '';
  }
  return templateObj?.title || '';
};

const getNewFlowResponseOptions = (user, isNewChat) => {
  let responseMessage = isNewChat
    ? 'Welcome to Boo Support. What can we help you with?\nChoose from one option below:'
    : 'Welcome back to Boo Support. What can we help you with?\nChoose from one option below:';

  let automatedChatOptions = topics;
  let stage = 'topics';

  // For banned users, instead of creating automated flow, send a message to email support
  const sendBannedAutoReply = user.banned || user.shadowBanned || (user.tempBanEndAt > Date.now());
  if (sendBannedAutoReply) {
    responseMessage = `I'm sorry to hear. For better assistance, please email <NAME_EMAIL> with your Boo ID and a description of the issue. Thank you for your patience.`;
    automatedChatOptions = undefined;
    stage = null;
  }

  return {
    responseMessage,
    automatedChatOptions,
    stage,
  };
};

const createOrUpdateTracking = async (chat, user, outcome, messageToSupport = null) => {
  let tracking;
  if (chat.automatedChatState.trackId) {
    tracking = await TrackAutoreponseUsage.findById(chat.automatedChatState.trackId);
    tracking.outcome = outcome;
    if (messageToSupport) {
      tracking.messageToSupport = messageToSupport;
    }
  } else {
    tracking = new TrackAutoreponseUsage({
      user: user._id,
      topic: chat.automatedChatState.topic,
      issue: chat.automatedChatState.issue,
      subIssue: chat.automatedChatState.subIssue,
      outcome,
      messageToSupport,
    });
    chat.automatedChatState.trackId = tracking._id.toString();
    await chat.save();
  }
  await tracking.save();
};

const createMessage = async (chat, user, text, options) => {
  const message = new Message({
    createdAt: Date.now(),
    chat: chat._id,
    text,
    sender: user._id,
    automatedChatOptions: options && options.length > 0 ? options : undefined,
  });
  return await message.save();
};

const createTranslatedReply = async (chat, sender, text, options, reciever) => {
  const message = new Message({
    createdAt: Date.now(),
    chat: chat._id,
    text: translate(text, reciever?.locale || 'en'),
    sender: sender._id,
    automatedChatOptions: options && options.length > 0 ? options.map((option) => ({
      next_step: option.next_step,
      title: translate(option.title, reciever?.locale || 'en'),
      ...(reciever?.versionAtLeast('1.13.74') && { en_title: option.en_title }),
    })) : undefined,
  });
  return await message.save();
};

const updateChat = async (chat, lastMessage, user) => {
  chat.lastMessage = lastMessage;
  chat.lastMessageTime = Date.now();
  chat.incrementNumMessages();
  chatLib.incrementUnreadMessages(chat, user._id);
  chat.perUserState = []
  chat.users.forEach(userId => {
    chat.perUserState.push({
      userId,
      unread: userId == user._id,
    });
  });
  await chat.save();
};

const buildOptionsResponse = async (chat, botUser, replyText, stage, options, user, userMessage) => {
  const supportMessage = await createTranslatedReply(chat, botUser, replyText, options, user);
  chat.automatedChatState.stage = stage;
  await updateChat(chat, supportMessage, user);

  return {
    userMessage: userMessage ? messageLib.formatMessage(userMessage) : undefined,
    message: messageLib.formatMessage(supportMessage, false, user, false, true),
    canWriteMessage: stage === 'message_for_support' || false,
  };
};

const handleTemplateNextStage = async (selectedItem, chat, user, botUser, nextStepOptions) => {
  const selectedTemplate = templates[selectedItem.next_step];
  /*
  if (!selectedTemplate?.next_step) {
    const options = [...topics, { title: 'No', next_step: 'end' }];
    return autoReplyTimeout(
      chat,
      botUser,
      'Is there anything else I can assist you with? If so, please select one of the options below:',
      'topics',
      user,
      options,
    );
  }
  */
  // v2 update: If there is no next step with template, ask if the user wants more assistance and redirect to support
  if (!selectedTemplate.next_step) {
    return autoReplyTimeout(chat, botUser, `Would you like additional assistance with this?`, 'support_or_topics', user, nextStepOptions);
  }
  return autoReplyTimeout(chat, botUser, 'Would you like more information on this?', 'sub_templates', user, [
    { title: 'Yes', next_step: selectedTemplate.next_step },
    { title: 'No', next_step: 'end' },
  ]);
};

const autoReplyTimeout = (chat, botUser, messageText, nextStage, user, options) => {
  setTimeout(async () => {
    const supportMessage = await createTranslatedReply(chat, botUser, messageText, options, user);
    chat.automatedChatState.stage = nextStage;
    await updateChat(chat, supportMessage, user);

    await sendSocketEvent(user._id, 'automatedChatAutoResponse', {
      message: messageLib.formatMessage(supportMessage, false, user, false, true),
      canWriteMessage: false,
    });
  }, process.env.TESTING ? 100 : 3000);
};

const handleSupportOrTopicsStage = async (selectedItem, chat, user, botUser) => {
  if (selectedItem?.next_step === 'support') {
    return await handleSupportStage(selectedItem, chat, user, botUser);
  }

  // User selected 'See all topics' option
  const userMessage = await createMessage(chat, user, selectedItem?.title || '');
  Object.assign(chat.automatedChatState, { trackId: null, topic: null, issue: null, subIssue: null });
  return buildOptionsResponse(chat, botUser, 'Please select one of the options below:', 'topics', topics, user, userMessage);
};

const handleTopicsStage = async (selectedItem, chat, user, botUser) => {
  const userMessage = await createMessage(chat, user, selectedItem?.title || '');
  const selectedTopic = selectedItem?.en_title || selectedItem?.title;
  Object.assign(chat.automatedChatState, { trackId: null, topic: selectedTopic, issue: null, subIssue: null });

  if (selectedItem?.next_step === 'support') {
    return buildOptionsResponse(
      chat,
      botUser,
      'Would you like additional assistance with this?',
      'support_or_topics',
      supportOptions,
      user,
      userMessage,
    );
  }

  if (selectedItem?.next_step?.includes('_issues')) {
    return buildOptionsResponse(chat, botUser, 'Please select one of the options below:', 'issues', issues[selectedItem.next_step], user, userMessage);
  }

  return {};
};

const handleIssuesStage = async (selectedItem, chat, user, botUser) => {
  const userMessage = await createMessage(chat, user, selectedItem?.title || '');
  chat.automatedChatState.issue = selectedItem?.en_title || selectedItem?.title;

  if (selectedItem.next_step.includes('template')) {
    await createOrUpdateTracking(chat, user, 'template_sent');
    handleTemplateNextStage(selectedItem, chat, user, botUser, supportOptions);
    const templateMsg = getTemplateText(user, templates[selectedItem.next_step]);
    return buildOptionsResponse(chat, botUser, templateMsg, 'templates', [], user, userMessage);
  }

  if (selectedItem.next_step === 'support') {
    return buildOptionsResponse(chat, botUser, 'Would you like additional assistance with this?', 'support_or_topics', supportOptions, user, userMessage);
  }

  if (selectedItem.next_step === 'topics') {
    Object.assign(chat.automatedChatState, { trackId: null, topic: null, issue: null, subIssue: null });
    return buildOptionsResponse(chat, botUser, 'Please select one of the options below:', 'topics', topics, user, userMessage);
  }

  return buildOptionsResponse(chat, botUser, 'Please select one of the options below:', 'sub_issues', subIssues[selectedItem.next_step], user, userMessage);
};

const handleSubissuesStage = async (selectedItem, chat, user, botUser) => {
  const userMessage = await createMessage(chat, user, selectedItem?.title || '');
  chat.automatedChatState.subIssue = selectedItem?.en_title || selectedItem?.title;
  handleTemplateNextStage(selectedItem, chat, user, botUser, supportOptions);
  await createOrUpdateTracking(chat, user, 'template_sent');
  const templateMsg = getTemplateText(user, templates[selectedItem.next_step]);
  return buildOptionsResponse(chat, botUser, templateMsg, 'templates', [], user, userMessage);
};

const handleSubTemplatesStage = async (selectedItem, chat, user, botUser) => {
  const userMessage = await createMessage(chat, user, selectedItem?.title || '');
  handleTemplateNextStage(selectedItem, chat, user, botUser, supportOptions);
  const templateMsg = getTemplateText(user, templates[selectedItem.next_step]);
  return buildOptionsResponse(chat, botUser, templateMsg, 'sub_templates', [], user, userMessage);
};

const handleSupportStage = async (selectedItem, chat, user, botUser) => {
  const userMessage = await createMessage(chat, user, selectedItem?.title || '');
  await createOrUpdateTracking(chat, user, 'support_requested');

  return buildOptionsResponse(
    chat,
    botUser,
    'Could you please describe your issue in more detail so we can assist you better?',
    'message_for_support',
    [],
    user,
    userMessage,
  );
};

const handleMessageForSupportStage = async (message, chat, user, botUser, booSupportUser) => {
  if (!message) return {};
  let userMessage = await createMessage(chat, user, message);
  await createOrUpdateTracking(chat, user, 'forwarded', message);

  if (!chat.users.includes(booSupportUser._id)) {
    chat.users.push(booSupportUser._id);

    // create system message
    const systemMessage = await Message.create({
      createdAt: Date.now(),
      chat: chat._id,
      text: `${botUser.firstName} invited ${booSupportUser.firstName} to the chat.`,
      sender: '',
      systemMessage: true,
    });

    Object.assign(chat.automatedChatState, { stage: null, trackId: null, topic: null, issue: null, subIssue: null, supportAdded: true });
    await updateChat(chat, systemMessage, booSupportUser);
    userMessage = messageLib.formatMessage(userMessage);

    const updatedChat = await Chat.findById(chat._id).populate('users').populate('lastMessage');
    // send approved chat for support user
    await sendSocketEvent(booSupportUser._id, 'approved chat', chatLib.formatChat(updatedChat.toObject({ flattenMaps: true }), booSupportUser));
    // Send user's message notification to support user
    messageLib.sendMessageNotifications(user, updatedChat, userMessage, userMessage.text);

    // send support added for user
    await sendSocketEvent(user._id, 'supportAdded in automatedChat', chatLib.formatChat(updatedChat.toObject({ flattenMaps: true }), user));
    // Send system message to user
    await sendSocketEvent(user._id, 'message', messageLib.formatMessage(systemMessage, null, user));

    // send support discontinuation message
    chatLib.sendAutomatedSupportReply(updatedChat, user, booSupportUser);
  }

  return {
    userMessage,
    canWriteMessage: true,
  };
};

const handleEndStage = async (selectedItem, chat, user, botUser) => {
  const userMessage = await createMessage(chat, user, selectedItem?.title || '');
  // There's a flow Topic -> Other -> No, where user can end the flow without any template/forwaring to support, so remove the trackId check from here because new track record should be created for that flow
  await createOrUpdateTracking(chat, user, 'resolved');

  const farewellMessage = `Thank you for reaching out to Boo support. We’re glad we could assist you. If you have any further questions, feel free to contact us again. Have a great day!`;

  const supportMessage = await createTranslatedReply(chat, botUser, farewellMessage, [], user);

  // User marked the issue as resolved, so show the support rating popup
  if (!chat.automatedChatState.rateSupportShownV2) {
    sendSocketEvent(user._id, 'automatedChat session end', {});
    chat.automatedChatState.rateSupportShown = undefined;
    chat.automatedChatState.rateSupportShownV2 = true;
  }

  Object.assign(chat.automatedChatState, { stage: null, trackId: null, topic: null, issue: null, subIssue: null });
  await updateChat(chat, supportMessage, user);

  return {
    userMessage: messageLib.formatMessage(userMessage),
    message: messageLib.formatMessage(supportMessage),
    canWriteMessage: false,
  };
};

const handleInactiveAutomatedChatMessage = async (chat, user, booSupportUser, messageText, mediaKey, path) => {
  if (!booSupportUser) {
    booSupportUser = await User.findById(chatLib.BOO_SUPPORT_ID);
    if (!booSupportUser) return null;
  }

  // Add system message to the chat that support user left the chat
  const systemMessage = await Message.create({
    chat: chat._id,
    text: `${booSupportUser?.firstName} left the chat.`,
    sender: '',
    systemMessage: true,
  });
  await sendSocketEvent(user._id, 'message', messageLib.formatMessage(systemMessage, null, user));

  // update chat users
  chat.users = chat.users.filter((x) => x._id.toString() !== booSupportUser._id.toString());

  // Add user message to the chat
  let userMessage;
  if (mediaKey && path) {
    userMessage = await Message.create({
      chat: chat._id,
      text: `${user.firstName} sent a ${mediaKey}!`,
      [mediaKey]: path,
      sender: user._id,
    });
  } else {
    userMessage = await createMessage(chat, user, messageText);
  }
  chat.incrementNumMessages();

  // Prepare reply message
  const { responseMessage, automatedChatOptions, stage } = getNewFlowResponseOptions(user);
  const savedReply = await createTranslatedReply(chat, { _id: chatLib.BOO_BOT_ID }, responseMessage, automatedChatOptions, user);

  Object.assign(chat.automatedChatState, { stage, trackId: null, topic: null, issue: null, subIssue: null, supportAdded: false });
  await updateChat(chat, savedReply, user);

  // send socket event to user
  const formattedChat = chatLib.formatChat(chat.toObject({ flattenMaps: true }), user);
  await sendSocketEvent(user._id, 'support left automatedChat', formattedChat);

  const result = {
    message: messageLib.formatMessage(savedReply, false, user, false, true),
    canWriteMessage: false,
  };
  await sendSocketEvent(user._id, 'automatedChatAutoResponse', result);
  return messageLib.formatMessage(userMessage);
};

module.exports = {
  handleTopicsStage,
  handleIssuesStage,
  handleSubissuesStage,
  handleSubTemplatesStage,
  handleSupportStage,
  handleMessageForSupportStage,
  handleEndStage,
  handleSupportOrTopicsStage,
  createMessage,
  createTranslatedReply,
  updateChat,
  getNewFlowResponseOptions,
  handleInactiveAutomatedChatMessage,
};
