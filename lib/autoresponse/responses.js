/*
From version 2
 - every flow that ends with a template will have option to be forwarded to support team
 - Markdown format for link is introduced, v2_title is added to templates where markdown is needed
 - en_title key is added in topic, issue, subIssue to track flow in english
*/
const supportOptions = [
  { title: 'Yes', en_title: 'Yes', next_step: 'support' },
  { title: 'No', en_title: 'No', next_step: 'end' },
  {
    title: 'See all topics',
    en_title: 'See all topics',
    next_step: 'topics',
  },
];

const topics = [
  {
    title: 'Account',
    en_title: 'Account',
    next_step: 'account_issues',
  },
  {
    title: 'Features',
    en_title: 'Features',
    next_step: 'features_issues',
  },
  {
    title: 'Purchases',
    en_title: 'Purchases',
    next_step: 'purchases_issues',
  },
  {
    title: 'Troubleshooting',
    en_title: 'Troubleshooting',
    next_step: 'troubleshooting_issues',
  },
  {
    title: 'Safety/Privacy Concerns',
    en_title: 'Safety/Privacy Concerns',
    next_step: 'safety_privacy_issues',
  },
  {
    title: 'Other',
    en_title: 'Other',
    next_step: 'support',
  },
];

const issues = {
  account_issues: [
    {
      title: 'Banned account',
      en_title: 'Banned account',
      next_step: 'banned_account_template',
    },
    {
      title: 'Appeal violation',
      en_title: 'Appeal violation',
      next_step: 'banned_account_template',
    },
    {
      title: 'Verification issues',
      en_title: 'Verification issues',
      next_step: 'verification_issues_template',
    },
    {
      title: 'Delete account',
      en_title: 'Delete account',
      next_step: 'delete_account_template',
    },
    {
      title: 'Update email',
      en_title: 'Update email',
      next_step: 'update_email_template',
    },
    {
      title: 'See all topics',
      en_title: 'See all topics',
      next_step: 'topics',
    },
  ],
  features_issues: [
    {
      title: 'New Souls/For You',
      en_title: 'New Souls/For You',
      next_step: 'new_souls_for_you_template',
    },
    {
      title: 'Matching & messaging',
      en_title: 'Matching & messaging',
      next_step: 'matching_messaging_template',
    },
    {
      title: 'Profile settings',
      en_title: 'Profile settings',
      next_step: 'profile_settings_template',
    },
    {
      title: 'Subscription',
      en_title: 'Subscription',
      next_step: 'subscription_template',
    },
    {
      title: 'In-app purchases (Super Loves, Coins, Boost)',
      en_title: 'In-app purchases (Super Loves, Coins, Boost)',
      next_step: 'in_app_purchases_template',
    },
    {
      title: 'Photo verification',
      en_title: 'Photo verification',
      next_step: 'verification_issues_template',
    },
    {
      title: 'Notifications',
      en_title: 'Notifications',
      next_step: 'notifications_template',
    },
    {
      title: 'Ghost Meter',
      en_title: 'Ghost Meter',
      next_step: 'ghost_meter_template',
    },
    {
      title: 'Other',
      en_title: 'Other',
      next_step: 'support',
    },
    {
      title: 'See all topics',
      en_title: 'See all topics',
      next_step: 'topics',
    },
  ],
  purchases_issues: [
    {
      title: 'Report credit card fraud',
      en_title: 'Report credit card fraud',
      next_step: 'support',
    },
    {
      title: 'Request refund',
      en_title: 'Request refund',
      next_step: 'request_refund_sub_issues',
    },
    {
      title: 'Missing subscription',
      en_title: 'Missing subscription',
      next_step: 'missing_subscription_template',
    },
    {
      title: 'Missing in-app purchase (Super Loves, Coins, Boost)',
      en_title: 'Missing in-app purchase (Super Loves, Coins, Boost)',
      next_step: 'missing_in_app_purchase_template',
    },
    {
      title: 'Cancel subscription',
      en_title: 'Cancel subscription',
      next_step: 'cancel_subscription_sub_issues',
    },
    {
      title: 'Payment issues',
      en_title: 'Payment issues',
      next_step: 'payments_issues_template',
    },
    {
      title: 'See all topics',
      en_title: 'See all topics',
      next_step: 'topics',
    },
  ],
  troubleshooting_issues: [
    {
      title: 'No souls detected',
      en_title: 'No souls detected',
      next_step: 'no_souls_detected_sub_issues',
    },
    {
      title: 'Missing messages',
      en_title: 'Missing messages',
      next_step: 'missing_messages_template',
    },
    {
      title: 'Revival power-up issues',
      en_title: 'Revival power-up issues',
      next_step: 'no_revival_template',
    },
    {
      title: 'Verification issues',
      en_title: 'Verification issues',
      next_step: 'verification_issues_sub_issues',
    },
    {
      title: 'Payment issues',
      en_title: 'Payment issues',
      next_step: 'payments_issues_template',
    },
    {
      title: 'Other',
      en_title: 'Other',
      next_step: 'support',
    },
    {
      title: 'See all topics',
      en_title: 'See all topics',
      next_step: 'topics',
    },
  ],
  safety_privacy_issues: [
    {
      title: 'Report online incident',
      en_title: 'Report online incident',
      next_step: 'support',
    },
    {
      title: 'Report physical harm',
      en_title: 'Report physical harm',
      next_step: 'support',
    },
    {
      title: 'Impersonation',
      en_title: 'Impersonation',
      next_step: 'impersonation_template',
    },
    {
      title: `Concern for another user's safety`,
      en_title: `Concern for another user's safety`,
      next_step: 'support',
    },
    {
      title: 'Hacked account',
      en_title: 'Hacked account',
      next_step: 'support',
    },
    {
      title: 'Data export',
      en_title: 'Data export',
      next_step: 'data_export_template',
    },
    {
      title: 'Chat export',
      en_title: 'Chat export',
      next_step: 'chat_export_template',
    },
    {
      title: 'Data-related questions',
      en_title: 'Data-related questions',
      next_step: 'privacy_policy_template',
    },
    {
      title: 'See all topics',
      en_title: 'See all topics',
      next_step: 'topics',
    },
  ],
};

const subIssues = {
  request_refund_sub_issues: [
    {
      title: 'Google',
      en_title: 'Google',
      next_step: 'refund_google_initial_template',
    },
    {
      title: 'Apple',
      en_title: 'Apple',
      next_step: 'refund_apple_initial_template',
    },
    {
      title: 'Web',
      en_title: 'Web',
      next_step: 'refund_web_template',
    },
  ],
  cancel_subscription_sub_issues: [
    {
      title: 'Google',
      en_title: 'Google',
      next_step: 'google_cancel_subscription_template',
    },
    {
      title: 'Apple',
      en_title: 'Apple',
      next_step: 'apple_cancel_subscription_template',
    },
    {
      title: 'Web',
      en_title: 'Web',
      next_step: 'web_cancel_subscription_template',
    },
  ],
  no_souls_detected_sub_issues: [
    {
      title: 'No Revival power-up used',
      en_title: 'No Revival power-up used',
      next_step: 'no_revival_template',
    },
    {
      title: 'After using Revival power-up',
      en_title: 'After using Revival power-up',
      next_step: 'after_revival_template',
    },
  ],
  verification_issues_sub_issues: [
    {
      title: 'Unsupported Browser',
      en_title: 'Unsupported Browser',
      next_step: 'unsupported_browser_template',
    },
    {
      title: 'Error Connection Reset',
      en_title: 'Error Connection Reset',
      next_step: 'error_connection_reset_template',
    },
    {
      title: 'SSL Error',
      en_title: 'SSL Error',
      next_step: 'error_connection_reset_template',
    },
    {
      title: 'Secure Session Expired',
      en_title: 'Secure Session Expired',
      next_step: 'secure_session_expired_template',
    },
    {
      title: 'Stuck at Loading',
      en_title: 'Stuck at Loading',
      next_step: 'stuck_at_loading_template',
    },
    {
      title: 'Other',
      en_title: 'Other',
      next_step: 'verification_issues_template',
    },
  ],
};

const templates = {
  banned_account_template: {
    title: `We ban accounts in response to violations of our Community Guidelines, which you can review here: https://boo.world/community-guidelines. \n\n\nIf you believe your account was banned in error, you can appeal by reaching out to <NAME_EMAIL>.`,
    v2_title: `We ban accounts in response to violations of our [Community Guidelines](https://boo.world/community-guidelines). If you believe your account was banned in error, you can appeal by reaching out to <NAME_EMAIL>.`,
  },
  verification_issues_template: {
    title: `To verify your account, your first photo must clearly show your face. Make sure you are in direct light, without any accessories, and facing directly toward the camera while completing the verification. Once you’ve done this, your verification should go through smoothly. If you have any other questions, we’re here to help!`,
  },
  delete_account_template: {
    title: `You can delete your account in Settings by following these steps:\n\n\n1. Go to the Menu (☰ icon in the upper left corner).\n2. Tap "Settings."\n3. Tap "My Account."\n4. Then tap "Delete Account."\n\n\nIf you’re looking to take a break instead, you can pause your account:\n\n\n1. Go to the Menu (☰ icon in the upper left corner).\n2. Tap "Settings."\n3. Tap "My Account."\n4. Tap "Pause Account."\n\n\nHope that helps!`,
  },
  update_email_template: {
    title: `To change your email:\n\n\n1. Go to the Menu (☰ icon in the upper left corner).\n2. Tap Settings.\n3. Select My Account.\n4. Tap Change Email.\n\n\nHope that helps!`,
  },
  sign_up_issues_template: {
    title: `Could you please send us the error message you're receiving when trying to sign up? This will help us resolve the issue more quickly.`,
  },
  new_souls_for_you_template: {
    title: `New Souls are recommended souls that you can either send Love to or X on. If both souls send Love, you get matched.\n\n\nThe For You feature provides tailored recommendations that refresh daily. Currently, you can only connect with these souls using Super Loves, which are available for purchase. However, we're working on ideas to implement Super Loves for free users in the future.\n\n\nHope this helps!`,
  },
  matching_messaging_template: {
    title: `Matching happens when both souls send Love to each other. You can also send a message with your request, which costs 50 coins, or it's free for Boo Infinity subscribers.\n\n\nWhen someone sends you a request, you can view them in the New Souls section, and you don’t need a subscription to match with them.\n\n\nHope this helps!`,
  },
  profile_settings_template: {
    title: `Could you please let us know what issue you're experiencing with your profile settings? This will help us assist you better.`,
  },
  subscription_template: {
    title: `Here’s what our subscription offers:\n\n\n- Unlimited Loves\n- Unlimited DMs\n- 2 Super Loves per week\n- Ninja Mode\n- See Who Viewed You\n- Read Receipts\n- Country Filter\n- Teleport\n- Time Travel\n\n\nFeel free to ask if you have any more questions!`,
  },
  in_app_purchases_template: {
    title: `Here’s how each feature works:\n\n\n- Super Love: When you use a Super Love, your message gets pinned to the top of the request list. This allows the recipient to see your profile and match with you even if they don't have a subscription.\n- Coins: These can be used to send Messages or to buy power-ups like Time Travel and Revival.\n- Boost: This feature makes you the top soul in your area for 60 minutes, which helps you get more matches by increasing your visibility.\n\n\nHope this helps!`,
  },
  notifications_template: {
    title: `To stay up to date with notifications, you can enable them by going to:\n\n\n1. Menu (☰ icon, upper left)\n2. Settings\n3. Notifications\n\n\nThis will ensure you receive updates on important activities and features.`,
  },
  ghost_meter_template: {
    title: `The Ghost Meter encourages meaningful connections and ensure every match gets the attention it deserves.\n\n\nHow it works:\n\n- You can have up to 10 open matches that haven’t been responded to yet.\n- To receive or accept new matches, simply reply to your open matches or unmatch with users you haven’t responded to.\n\n\nThis helps create a more engaging experience for everyone on the platform. Thank you for understanding!`,
  },
  missing_subscription_template: {
    title: `If you've purchased Boo Infinity but it hasn't activated. Please follow these steps:\n\n\n1. Open the Boo app.\n2. Go to "Settings."\n3. Select "My Account."\n4. Choose the option to retry pending purchases.\n\n\nIf the issue continues, before attempting another purchase:\n- For Google/Apple payments: please send the GPA code and the email address linked to the payment.\n- For web payments: please provide the last 4 digits of your card and the email associated with the payment method.`,
  },
  missing_in_app_purchase_template: {
    title: `If you've made a purchase but haven’t received it yet, please follow these steps:\n\n\n1. Open the Boo app.\n2. Go to "Settings."\n3. Select "Account."\n4. Choose the option to retry pending purchases.\n\n\nIf the issue persists, before making another purchase:\n- For Google/Apple payments: please send us the GPA/Order ID and the email address linked to the payment.\n- For web payments: please provide the last 4 digits of your card and the email associated with the payment method.`,
  },
  payments_issues_template: {
    title: `Could you please provide us more details on the error you’re encountering with the payment?`,
  },
  missing_messages_template: {
    title: `If you're missing messages, it could be due to one of the following reasons:\n\n\n- The user may have unmatched with you.\n- They might have deleted their account.\n- They might have blocked you.\n- Their account could have been banned.\n\n\nWe hope this helps clarify the situation.`,
  },
  login_method_forgotten_template: {
    title: `To help you recover your login method, could you please provide the Boo ID associated with your account or the mobile number you used? This will assist us in locating your account and guiding you through the next steps.`,
  },
  impersonation_template: {
    title: `Thanks for bringing this up to our attention. We've submitted your report to our team for further investigation. If you have any additional information, such as the Boo ID of the account in question, please feel free to reach out to us. We appreciate your cooperation.`,
  },
  data_export_template: {
    title: `To download your data, follow these steps:\n\n\n1. Go to the Menu (☰ icon in the upper left).\n2. Select "Settings."\n3. Tap on "My Account."\n4. Choose "Download My Information."`,
  },
  chat_export_template: {
    title: `To download your chat with a specific soul, follow these steps:\n\n\n1. Go to Messages.\n2. Select the specific chat you want to download.\n3. Tap Settings (⋮ icon in the upper right)\n4. Choose Download Chat.\n\n\nPlease note that both users must follow these steps for the download to be successful.`,
  },
  refund_google_initial_template: {
    title: `You may request a refund directly from your app store provider. You may find instructions here:\n\n\n- Google Play Store: https://support.google.com/googleplay/answer/2479637?hl=en`,
    v2_title: `You may request a refund directly from your app store provider. You may find instructions here: [Google Play Store](https://support.google.com/googleplay/answer/2479637?hl=en)`,
    next_step: 'refund_google_sub_template',
  },
  refund_google_sub_template: {
    title: `To process your refund request, could you please provide the following details?\n\n\n1. A screenshot of the receipt with GPA code visible. Visit http://pay.google.com/ and select your order to see your receipt.\n2. The email address associated with the purchase.\n\n\nOnce we have this information, we’ll be able to assist you further.\n\n\nThank you!`,
    v2_title: `To process your refund request, could you please provide the following details?\n\n\n1.The GPA code of your purchase. Visit [Google Pay](http://pay.google.com/) and select your order to see your receipt.\n2. The email address associated with the purchase.\n\n\nOnce we have this information, we’ll be able to assist you further.\n\n\nThank you!`,
  },
  refund_apple_initial_template: {
    title: `You may request a refund directly from your app store provider. You may find instructions here:\n\n\n- Apple App Store: https://support.apple.com/en-us/HT202039`,
    v2_title: `You may request a refund directly from your app store provider. You may find instructions here: [Apple App Store](https://support.apple.com/en-us/HT202039)`,
    next_step: 'refund_apple_sub_template',
  },
  refund_apple_sub_template: {
    title: `To process your refund request, could you please provide the following details?\n\n\n1. The invoice date, and the order ID of your purchase. Visit https://reportaproblem.apple.com/ and select your order to see your receipt.\n2. The email address associated with the purchase.\n\n\nOnce we have this information, we’ll be able to assist you further.\n\n\nThank you!`,
    v2_title: `To process your refund request, could you please provide the following details?\n\n\n1. The invoice date, and the order ID of your purchase. Visit [Report a Problem](https://reportaproblem.apple.com/) and select your order to see your receipt.\n2. The email address associated with the purchase.\n\n\nOnce we have this information, we’ll be able to assist you further.\n\n\nThank you!`,
  },
  refund_web_template: {
    title: `To process your refund request, could you please provide the following details?\n\n\n1. The last 4 card digit of the payment method.\n2. The email address associated with the purchase.\n\n\nOnce we have this information, we’ll be able to assist you further.\n\n\nThank you!`,
  },
  google_cancel_subscription_template: {
    title: `To cancel your subscription, you can navigate to the app's subscription settings.\n\n\n1. Open the Google Play app.\n2. At the top right, tap the profile icon.\n3. Tap Payments & subscriptions. Subscriptions.\n4. Select the subscription you want to cancel.\n5. Tap Cancel subscription.`,
  },
  apple_cancel_subscription_template: {
    title: `To cancel your subscription, you can navigate to the app's subscription settings.\n\n\n1. Open Settings on your iPhone or iPad.\n2. Tap your Apple ID at the top of the page.\n3. Tap Subscriptions.\n4. Tap on the subscription in the list that you wish to cancel.\n5. Tap Cancel Subscription at the bottom.`,
  },
  web_cancel_subscription_template: {
    title: `To cancel your subscription, you can navigate to the app's subscription settings.\n\n\n1. Profile icon (upper right)\n2. Account\n3. Manage Billing`,
  },
  no_revival_template: {
    title: `To resolve the "No Soul Detected" error, please try resetting your filter settings by following these steps:\n\n\n1. Go to the "Match" section.\n2. Tap the Filter icon (☷) in the upper right corner.\n3. Select "Reset Settings" (↺ icon) in the upper right corner.\n\n\nThis should refresh your filters and help with the issue. If you continue to experience problems, please let us know!`,
  },
  after_revival_template: {
    title: `Thank you for bringing this to our attention. We've forwarded your concern to our team for further investigation. If you have any other questions or need additional assistance, please let us know!`,
  },
  unsupported_browser_template: {
    title: `The error occurs because iOS only supports Safari for verification. Please temporarily set Safari as your default browser:\n\n\n1. Go to Settings.\n2. Select your browser app.\n3. Tap Default Browser App and choose Safari.\n\n\nThen try verifying again. Let us know if you need help!`,
  },
  error_connection_reset_template: {
    title: `Please check your network connection or try using a VPN. If the problem persists, let us know! We're here to help.`,
  },
  secure_session_expired_template: {
    title: `The issue may be due to your device's time being incorrect. Please check and update your device time using these steps:\n\n\nFor Android:\n1. Go to Settings > General Management > Date and Time.\n2. Ensure Automatic date and time is enabled, or manually set the correct time.\n\nFor iPhone:\n1. Go to Settings > General > Date & Time.\n2. Enable Set Automatically, or manually adjust the time.\n\n\nLet us know if the issue persists!`,
  },
  stuck_at_loading_template: {
    title: `We recommend trying to reinstall the app to see if that resolves the issue. Please update us after reinstalling, and let us know if it works or if you continue to experience problems so we can assist further.`,
  },
  privacy_policy_template: {
    title: `For any data-related questions, please refer to our privacy policy here: https://boo.world/privacy-policy. Let us know if you need further assistance!`,
    v2_title: `For any data-related questions, please refer to our [Privacy Policy](https://boo.world/privacy-policy). Let us know if you need further assistance!`,
  },
};

module.exports = { topics, issues, subIssues, templates, supportOptions };
