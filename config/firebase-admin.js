const admin = require('firebase-admin');
const jwt = require('jsonwebtoken');
const http = require('../lib/http-errors');
const constants = require('../lib/constants');
const notificationLib = require('../lib/notification');
const User = require('../models/user');
const { translate } = require('../lib/translate');

const FIREBASE_ERROR_CODES = ['messaging/invalid-registration-token', 'messaging/registration-token-not-registered'];
const MAX_FCM_BATCH_SIZE = 500;

admin.initializeApp({
  credential: admin.credential.applicationDefault(),
});

// Re-use the App Check client for the lifetime of the process.
const appCheck = admin.appCheck();

function classifyAppCheckError(err) {
  if (!err?.code?.startsWith('app-check/')) return 'unknown';

  const fatal = [
    'app-check/invalid-argument',
    'app-check/token-expired',
    'app-check/token-already-consumed',
    'app-check/invalid-audience',
    'app-check/invalid-issuer',
    'app-check/invalid-subject',
    'app-check/unregistered-app',
  ];

  if (fatal.includes(err.code))       return 'unauthorized';   // 401/403
  if (err.code === 'app-check/quota-exceeded' ||
      err.message.includes('429'))    return 'rate-limit';     // 429
  if (err.code === 'app-check/internal-error') return 'transient'; // 503
  return 'unknown';
}

function sendNotif(user, category) {
  if (!user.pushNotificationSettings || !category) {
    return true;
  }

  if (user.pushNotificationSettings[category] == false) {
    return false;
  }

  return true;
}

async function removeInvalidFcmTokens(currentTokens, batchRes) {
  if (batchRes.failureCount > 0) {
    const tokensToRemove = [];
    batchRes.responses.forEach((sendRes, i) => {
      if (!sendRes.success && FIREBASE_ERROR_CODES.includes(sendRes.error.code)) {
        tokensToRemove.push(currentTokens[i]);
      }
    });
    console.log(`removing invalid fcmTokens : ${tokensToRemove}`);
    if (tokensToRemove.length > 0) {
      await User.updateMany(
        {
          fcmToken: { $in: tokensToRemove },
        },
        {
          $set: {
            fcmToken: null,
            fcmTokenUpdatedAt: Date.now(),
          },
        },
      );
    }
  }
}

function createMessageBody({ user, title, body, data, threadId, sound, channelId, analyticsLabel, notificationId, notificationBadgeCount }) {
  let message = {
    token: user.fcmToken,
    notification: { title, body },
    apns: { payload: { aps: { sound } } },
    android: {
      priority: 'high',
      notification: { channelId, sound },
    },
  };

  if (data) {
    message.data = data;
  }

  if (notificationBadgeCount) {
    message.apns.payload.aps.badge = notificationBadgeCount;
  }

  if (threadId) {
    message.apns.payload.aps['thread-id'] = threadId.toString();
  }

  if (notificationId && user.versionAtLeast('1.13.49')) {
    message = {
      token: user.fcmToken,
      data: { ...data, notificationId: notificationId.toString() },
    };

    if (title) message.data.title = title;
    if (body) message.data.body = body;
  }

  if (analyticsLabel) {
    message.fcmOptions = { analyticsLabel };
  }

  return message;
}

module.exports = {
  admin,

  verifyToken(req, res, next) {
    if (!req.headers.authorization) {
      return next(http.unauthenticatedError());
    }
    const idToken = req.headers.authorization;
    admin.auth().verifyIdToken(idToken)
      .then((decodedToken) => {
        console.log(`Firebase auth: User ${decodedToken.uid} ${req.method} ${req.originalUrl} ${JSON.stringify(req.body)}`);

        req.uid = decodedToken.uid;
        req.phoneNumber = decodedToken.phone_number;
        req.email = decodedToken.email;
        return next();
      }).catch((firebase_err) => {
        const decoded = jwt.decode(idToken);
        console.log(req.url, ' Firebase authentication error. idToken: ', idToken, ', decoded: ', decoded, ', firebase_err: ', firebase_err);
        return next(http.unauthenticatedError());
      });
  },

  async verifyAppCheck(req, res, next) {
    if (!req.user) {
      return next();
    }

    const token = req.header('X-Firebase-AppCheck');
    if (!token) {
      return next();
    }

    try {
      const claims = await appCheck.verifyToken(token);
      req.user.passedAppCheck = true;
      await req.user.save();
      return next();
    } catch (err) {
      console.error('App Check verification failed:', err);
      const category = classifyAppCheckError(err);
      req.user.appCheckErrorCode = err.code;
      if (category == 'unauthorized') {
        req.user.failedAppCheck = true;
      }
      await req.user.save();
      return next();
    }
  },

  async sendNotification(user, category, originalTitle, originalBody, data, threadId, soundCategory, analyticsLabel, doTranslate, notificationId, notificationMetric) {
    // Send a high priority notification

    if (!user || !sendNotif(user, category)) {
      return;
    }

    let title = originalTitle;
    let body = originalBody;
    if (doTranslate) {
      const locale = user.locale;
      title = translate(originalTitle, locale);
      const titleEnglish = translate(originalTitle, 'en');
      body = translate(originalBody, locale);
      const bodyEnglish = translate(originalBody, 'en');

      if (locale && locale !== 'en' && (title === titleEnglish || body === bodyEnglish)) {
        return;
      }
    }

    const truncate = (text) => (text.length > constants.maxNotificationBodyLength ? text.substring(0, constants.maxNotificationBodyLength) + '...' : text);
    title = truncate(title);
    body = truncate(body);

    if (!user.fcmToken) {
      return;
    }

    const sound = `${soundCategory}.wav` || 'default';
    const channelId = soundCategory || 'boo_notification_channel';

    let notificationBadgeCount;
    if (data?.action === 'delete') {
      notificationBadgeCount = await notificationLib.decreaseNotificationBadgeCount(user._id);
    } else {
      notificationBadgeCount = await notificationLib.incrementNotificationBadgeCount(user._id);
    }

    const message = createMessageBody({
      user,
      title,
      body,
      data,
      threadId,
      sound,
      channelId,
      analyticsLabel,
      notificationId,
      notificationBadgeCount,
    });

    const promise = admin.messaging().send(message)
      .then(() => {
        if (data?.action !== 'delete') {
          User.incrementMetrics(user._id, ['numNotifications', notificationMetric].filter(Boolean));
        }
      })
      .catch((error) => {
        if (FIREBASE_ERROR_CODES.includes(error.code)) {
          User.removeFcmToken(user._id, message.token);
          console.log(`User ${user._id}: removed fcmToken`);
        }
        User.incrementMetrics(user._id, ['numNotificationErrors']);
        console.log(`User ${user._id}: Error sending notification. Error: ${error}, notification: ${JSON.stringify(message, null, 2)}`);
      });

    if (process.env.TESTING) {
      await promise;
    }
  },

  /*
  // Multicast notification is deprecated by firebase.
  async sendMulticastNotifications(tokens, notification, analyticsLabel) {
    // filter out duplicate tokens
    tokens = [...tokens];

    console.log(`Number of tokens: ${tokens.length}`);

    for (let i = 0; i < tokens.length; i += MAX_FCM_BATCH_SIZE) {
      const currentTokens = tokens.slice(i, i + MAX_FCM_BATCH_SIZE);
      console.log('Sending notifications for: ', currentTokens);

      const { title } = notification;
      const { body } = notification;
      const { data } = notification;
      const sound = `${notification.sound}.wav` || 'default';
      const channelId = notification.sound || 'boo_notification_channel';

      const message = {
        notification: {
          title,
          body,
        },
        tokens: currentTokens,
        apns: {
          payload: {
            aps: {
              sound,
            },
          },
        },
        android: {
          priority: 'high',
          notification: {
            channelId,
            sound,
          },
        },
      };
      if (analyticsLabel) {
        message.fcmOptions = {
          analyticsLabel,
        }
      }
      if (data) {
        message.data = data;
      }
      console.log(message);
      const promise = admin.messaging().sendMulticast(message)
        .then(async (response) => {
          console.log('Successfully sent multicast: ', response);
          await removeInvalidFcmTokens(currentTokens, response);
        })
        .catch((error) => {
          console.log('Error sending multicast: ', error);
        });

      if (process.env.TESTING) {
        await promise;
      }
    }
  },
  */

  async sendNotificationToFriends(tokens, notification, analyticsLabel) {
    // filter out duplicate tokens
    tokens = [...tokens];
    console.log(`Number of tokens: ${tokens.length}`);
    const tokensToRemove = [];

    for (let i = 0; i < tokens.length; i++) {
      const currentToken = tokens[i];
      const { title } = notification;
      const { body } = notification;
      const { data } = notification;
      const sound = `${notification.sound}.wav` || 'default';
      const channelId = notification.sound || 'boo_notification_channel';

      const message = {
        notification: {
          title,
          body,
        },
        token: currentToken,
        apns: {
          payload: {
            aps: {
              sound,
            },
          },
        },
        android: {
          priority: 'high',
          notification: {
            channelId,
            sound,
          },
        },
      };
      if (analyticsLabel) {
        message.fcmOptions = {
          analyticsLabel,
        };
      }
      if (data) {
        message.data = data;
      }
      console.log(message);
      const promise = admin.messaging().send(message)
        .then(() => {
          console.log('Successfully sent notification to friend: ', currentToken);
        })
        .catch((error) => {
          if (FIREBASE_ERROR_CODES.includes(error.code)) {
            tokensToRemove.push(currentToken);
          }
        });

      if (process.env.TESTING) {
        await promise;
      }
    }
    if (tokensToRemove.length > 0) {
      await User.updateMany(
        {
          fcmToken: { $in: tokensToRemove },
        },
        {
          $set: {
            fcmToken: null,
            fcmTokenUpdatedAt: Date.now(),
          },
        },
      );
    }
  },
  FIREBASE_ERROR_CODES,
  MAX_FCM_BATCH_SIZE,
};
