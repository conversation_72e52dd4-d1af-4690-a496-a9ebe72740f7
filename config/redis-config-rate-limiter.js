const Redis = require('ioredis');
const redisUrl = process.env.REDIS_URI
let redisClientForLimiter = null;

const parseRedisUrl = (url) => {
  const urlObj = new URL(url);
  return {
    host: urlObj.hostname,
    port: parseInt(urlObj.port) || 6379,
  };
};

const initRedis = async () => {
  if (!redisUrl) return null
  let client;
  if (['beta','prod'].includes(process.env.NODE_ENV)) {
    const { host, port } = parseRedisUrl(redisUrl);
    client = new Redis.Cluster([{ host, port }]);
  } else {
    client = new Redis(redisUrl);
  }
  client.on('connect', () => {
    console.log(`Connected to Redis`);
    redisClientForLimiter = client;
  });
  client.on('error', (err) => {
    console.error('Redis connection error:', err);
    redisClientForLimiter = null;
  });
  client.on('end', () => {
    console.error('Redis connection closed.');
    redisClientForLimiter = null;
  });
  return client;
};

initRedis().catch((err) => {
  console.error('Failed to initialize Redis:', err);
  redisClientForLimiter = null;
});

const getRedisClientForLimiter = () => {
  return redisClientForLimiter
}
module.exports = { getRedisClientForLimiter }
