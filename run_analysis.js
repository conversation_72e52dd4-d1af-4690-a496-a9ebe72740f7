#!/usr/bin/env node

const mongoose = require('mongoose');
const UsersProfileAnalysisPrompts = require('./models/users-profile-analysis-prompts');

// UPDATE THIS CONNECTION STRING TO MATCH YOUR LOCAL DATABASE
const DB_CONNECTION = 'mongodb://localhost:27017/your_database_name';

async function runCompleteAnalysis() {
  try {
    console.log('🚀 Starting Complete Profile Analysis Issue Investigation\n');
    
    await mongoose.connect(DB_CONNECTION, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to database\n');
    
    // Get document counts
    const totalDocs = await UsersProfileAnalysisPrompts.countDocuments({});
    const errorDocs = await UsersProfileAnalysisPrompts.countDocuments({ isError: true });
    const successDocs = await UsersProfileAnalysisPrompts.countDocuments({
      isError: false,
      outputResults: { $exists: true, $ne: null, $ne: {}, $ne: [] }
    });
    const failedDocs = await UsersProfileAnalysisPrompts.countDocuments({
      isError: false,
      output: { $exists: true, $ne: null },
      $or: [
        { outputResults: { $exists: false } },
        { outputResults: null },
        { outputResults: {} },
        { outputResults: [] }
      ]
    });
    
    console.log('📊 DOCUMENT STATISTICS:');
    console.log(`Total documents: ${totalDocs}`);
    console.log(`Error documents (isError=true): ${errorDocs}`);
    console.log(`Success documents (has outputResults): ${successDocs}`);
    console.log(`Failed documents (empty outputResults): ${failedDocs}\n`);
    
    if (failedDocs === 0) {
      console.log('✅ No failed documents found. All documents have outputResults populated.');
      return;
    }
    
    if (successDocs === 0) {
      console.log('❌ No success documents found for comparison.');
      return;
    }
    
    // Get one of each for detailed analysis
    const successDoc = await UsersProfileAnalysisPrompts.findOne({
      isError: false,
      outputResults: { $exists: true, $ne: null, $ne: {}, $ne: [] }
    }).sort({ createdAt: -1 });
    
    const failedDoc = await UsersProfileAnalysisPrompts.findOne({
      isError: false,
      output: { $exists: true, $ne: null },
      $or: [
        { outputResults: { $exists: false } },
        { outputResults: null },
        { outputResults: {} },
        { outputResults: [] }
      ]
    }).sort({ createdAt: -1 });
    
    console.log('🔍 DETAILED ANALYSIS OF SAMPLE DOCUMENTS\n');
    
    // Analyze success document
    console.log('='.repeat(60));
    console.log('SUCCESS DOCUMENT ANALYSIS');
    console.log('='.repeat(60));
    analyzeDocument(successDoc, 'SUCCESS');
    
    console.log('\n' + '='.repeat(60));
    console.log('FAILED DOCUMENT ANALYSIS');
    console.log('='.repeat(60));
    analyzeDocument(failedDoc, 'FAILED');
    
    console.log('\n' + '='.repeat(60));
    console.log('ROOT CAUSE ANALYSIS');
    console.log('='.repeat(60));
    
    // Extract and compare the actual OpenAI responses
    const successOpenAI = extractOpenAIResponse(successDoc);
    const failedOpenAI = extractOpenAIResponse(failedDoc);
    
    if (successOpenAI && failedOpenAI) {
      compareOpenAIResponses(successOpenAI, failedOpenAI);
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('RECOMMENDATIONS');
    console.log('='.repeat(60));
    console.log('1. Run process_failed_document.js to see exact processing steps');
    console.log('2. Check if OpenAI response format has changed');
    console.log('3. Verify the nested structure condition in getpromptResponseForProfileAnalysis');
    console.log('4. Consider adding more robust error handling for different response formats');
    
  } catch (error) {
    console.error('❌ Error during analysis:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n✅ Analysis complete. Database connection closed.');
  }
}

function analyzeDocument(doc, type) {
  console.log(`📄 ${type} Document ID: ${doc._id}`);
  console.log(`📅 Created: ${doc.createdAt}`);
  console.log(`👤 User: ${doc.userId}`);
  console.log(`🤖 Model: ${doc.model}`);
  console.log(`❌ Is Error: ${doc.isError}`);
  console.log(`💰 Cost: ${doc.cost}`);
  console.log(`⏱️  Processing Time: ${doc.processingTime}ms`);
  
  // Analyze outputResults
  const outputResults = doc.outputResults;
  console.log(`📊 OutputResults Type: ${typeof outputResults}`);
  console.log(`📊 OutputResults Is Array: ${Array.isArray(outputResults)}`);
  
  if (Array.isArray(outputResults)) {
    console.log(`📊 OutputResults Length: ${outputResults.length}`);
  } else if (typeof outputResults === 'object' && outputResults !== null) {
    console.log(`📊 OutputResults Keys: ${Object.keys(outputResults).join(', ')}`);
  } else {
    console.log(`📊 OutputResults Value: ${JSON.stringify(outputResults)}`);
  }
  
  // Analyze raw output
  try {
    const outputData = JSON.parse(doc.output);
    console.log(`📝 Raw Output Keys: ${Object.keys(outputData).join(', ')}`);
    if (outputData.output) {
      console.log(`📝 OpenAI Response Type: ${typeof outputData.output}`);
      if (typeof outputData.output === 'string') {
        console.log(`📝 OpenAI Response Length: ${outputData.output.length} chars`);
      }
    }
  } catch (e) {
    console.log(`📝 Raw Output: Not JSON parseable`);
  }
}

function extractOpenAIResponse(doc) {
  try {
    const outputData = JSON.parse(doc.output);
    return outputData.output;
  } catch (e) {
    return doc.output;
  }
}

function compareOpenAIResponses(successResponse, failedResponse) {
  console.log('🔍 Comparing OpenAI Response Structures...\n');
  
  try {
    // Clean and parse both responses
    const cleanSuccess = successResponse.replace('```json', '').replace('```', '').replace(/\n/g, '').trim();
    const cleanFailed = failedResponse.replace('```json', '').replace('```', '').replace(/\n/g, '').trim();
    
    const successParsed = JSON.parse(cleanSuccess);
    const failedParsed = JSON.parse(cleanFailed);
    
    console.log('✅ Both responses parsed successfully');
    console.log(`📊 Success response keys: ${Object.keys(successParsed).join(', ')}`);
    console.log(`📊 Failed response keys: ${Object.keys(failedParsed).join(', ')}`);
    
    // Check the critical nested structure
    if (successParsed.output && failedParsed.output) {
      console.log('\n🎯 Checking Critical Nested Structure:');
      
      const successNested = checkNestedStructure(successParsed.output);
      const failedNested = checkNestedStructure(failedParsed.output);
      
      console.log(`Success has required nested structure: ${successNested.hasAll}`);
      console.log(`Failed has required nested structure: ${failedNested.hasAll}`);
      
      if (successNested.hasAll !== failedNested.hasAll) {
        console.log('\n🚨 ROOT CAUSE IDENTIFIED:');
        console.log('The nested structure condition differs between success and failed documents!');
        console.log('This explains why outputResults is empty for failed documents.');
        
        console.log('\n📋 Detailed Structure Check:');
        console.log('SUCCESS:', successNested);
        console.log('FAILED:', failedNested);
      }
    }
    
  } catch (parseError) {
    console.log('❌ Error parsing OpenAI responses:', parseError.message);
  }
}

function checkNestedStructure(output) {
  return {
    hasInsightsOverall: !!output.insights_overall,
    hasInsightsBiography: !!output.insights_overall?.insights_biography,
    hasInsightsPhotos: !!output.insights_overall?.insights_photos,
    hasInsightsPrompts: !!output.insights_overall?.insights_prompts,
    hasAll: !!(output.insights_overall && 
              output.insights_overall.insights_biography && 
              output.insights_overall.insights_photos &&
              output.insights_overall.insights_prompts),
    outputKeys: Object.keys(output).join(', '),
    insightsOverallKeys: output.insights_overall ? Object.keys(output.insights_overall).join(', ') : 'N/A'
  };
}

// Run the analysis
if (require.main === module) {
  runCompleteAnalysis();
}

module.exports = { runCompleteAnalysis };
