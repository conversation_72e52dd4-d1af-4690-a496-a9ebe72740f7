const mongoose = require('mongoose');
const UsersProfileAnalysisPrompts = require('./models/users-profile-analysis-prompts');

// Connect to your local database - UPDATE THIS CONNECTION STRING
mongoose.connect('mongodb://localhost:27017/your_database_name', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Simulate the exact processing logic from getpromptResponseForProfileAnalysis
function simulateProcessing(rawOutput, promptsLength = 0, locale = 'en') {
  let formattedOutputResults = [];
  let isError = false;
  let errorMessage = null;

  console.log('🔄 Starting simulation of getpromptResponseForProfileAnalysis processing...\n');

  if (rawOutput) {
    try {
      console.log('📝 Raw output received:', rawOutput.substring(0, 500) + '...\n');

      const cleanedOutput = rawOutput.replace('```json', '').replace('```', '').replace(/\n/g, '').trim();
      console.log('🧹 After cleaning (removing ```json, ```, newlines):', cleanedOutput.substring(0, 500) + '...\n');

      const parsedOutput = JSON.parse(cleanedOutput);
      console.log('✅ JSON parsing successful');
      console.log('📊 Parsed output type:', typeof parsedOutput);
      console.log('📊 Parsed output keys:', Object.keys(parsedOutput).join(', '), '\n');

      if (parsedOutput?.output && typeof parsedOutput.output === 'object') {
        console.log('✅ parsedOutput.output exists and is object');
        console.log('📊 parsedOutput.output keys:', Object.keys(parsedOutput.output).join(', '), '\n');

        // Check the critical condition
        const hasInsightsOverall = parsedOutput.output.insights_overall;
        const hasInsightsBiography = parsedOutput.output.insights_overall?.insights_biography;
        const hasInsightsPhotos = parsedOutput.output.insights_overall?.insights_photos;
        const hasInsightsPrompts = parsedOutput.output.insights_overall?.insights_prompts;

        console.log('🔍 Checking nested structure conditions:');
        console.log(`- insights_overall exists: ${!!hasInsightsOverall}`);
        console.log(`- insights_overall.insights_biography exists: ${!!hasInsightsBiography}`);
        console.log(`- insights_overall.insights_photos exists: ${!!hasInsightsPhotos}`);
        console.log(`- insights_overall.insights_prompts exists: ${!!hasInsightsPrompts}\n`);

        if (hasInsightsOverall && hasInsightsBiography && hasInsightsPhotos && hasInsightsPrompts) {
          console.log('✅ NESTED STRUCTURE CONDITION MET - Using nested data extraction');
          const nestedData = parsedOutput.output.insights_overall;
          formattedOutputResults = {
            insights_overall: { overall: nestedData.overall },
            insights_biography: nestedData.insights_biography,
            insights_photos: nestedData.insights_photos,
            insights_prompts: nestedData.insights_prompts
          };
          console.log('📦 Formatted output results (nested):', Object.keys(formattedOutputResults).join(', '));
        } else {
          console.log('❌ NESTED STRUCTURE CONDITION NOT MET - Using direct spread');
          formattedOutputResults = { ...parsedOutput.output };
          console.log('📦 Formatted output results (spread):', Object.keys(formattedOutputResults).join(', '));
        }

        console.log('\n🔧 Processing cleanup operations...');

        // Cleanup operations
        if(formattedOutputResults?.insights_overall?.items) {
          console.log('🗑️  Deleting insights_overall.items');
          delete formattedOutputResults.insights_overall.items;
        }
        if(formattedOutputResults?.insights_biography?.items) {
          console.log('🗑️  Deleting insights_biography.items');
          delete formattedOutputResults.insights_biography.items;
        }
        if(formattedOutputResults?.insights_prompts?.items?.length) {
          console.log(`🔪 Slicing insights_prompts.items to ${promptsLength} items`);
          formattedOutputResults.insights_prompts.items = formattedOutputResults.insights_prompts.items.slice(0, promptsLength);
        } else if(formattedOutputResults.insights_prompts) {
          console.log('📝 Setting insights_prompts.items to empty array');
          formattedOutputResults.insights_prompts.items = [];
        }

      } else {
        console.log('❌ parsedOutput.output does not exist or is not an object');
        console.log('📊 parsedOutput.output type:', typeof parsedOutput.output);
        console.log('📊 parsedOutput.output value:', parsedOutput.output);
      }

    } catch (err) {
      console.log("❌ Error parsing JSON or processing results:", err.message);
      isError = true;
      errorMessage = `JSON parsing error: ${err.message}`;
    }
  } else {
    console.log('❌ No raw output provided');
  }

  console.log('\n📋 FINAL RESULTS:');
  console.log('- formattedOutputResults type:', typeof formattedOutputResults);
  console.log('- formattedOutputResults is array:', Array.isArray(formattedOutputResults));
  console.log('- formattedOutputResults keys/length:', Array.isArray(formattedOutputResults) ? formattedOutputResults.length : Object.keys(formattedOutputResults).join(', '));
  console.log('- isError:', isError);
  console.log('- errorMessage:', errorMessage);

  return {
    formattedOutputResults,
    isError,
    errorMessage
  };
}

async function analyzeProfileAnalysisIssue() {
  try {
    console.log('🔍 Starting Profile Analysis Issue Investigation...\n');
    
    // Find all documents in the collection
    const allDocs = await UsersProfileAnalysisPrompts.find({}).sort({ createdAt: -1 }).limit(10);
    console.log(`📊 Found ${allDocs.length} documents in UsersProfileAnalysisPrompts collection\n`);
    
    // Separate success and failed documents
    const successDocs = allDocs.filter(doc => !doc.isError && doc.outputResults && Object.keys(doc.outputResults).length > 0);
    const failedDocs = allDocs.filter(doc => !doc.isError && doc.output && (!doc.outputResults || Object.keys(doc.outputResults).length === 0));
    const errorDocs = allDocs.filter(doc => doc.isError);
    
    console.log(`✅ Success documents (has outputResults): ${successDocs.length}`);
    console.log(`❌ Failed documents (has output but empty outputResults): ${failedDocs.length}`);
    console.log(`🚫 Error documents (isError = true): ${errorDocs.length}\n`);
    
    if (successDocs.length > 0 && failedDocs.length > 0) {
      console.log('🔬 ANALYZING DIFFERENCES BETWEEN SUCCESS AND FAILED DOCUMENTS\n');
      
      const successDoc = successDocs[0];
      const failedDoc = failedDocs[0];
      
      console.log('='.repeat(80));
      console.log('SUCCESS DOCUMENT ANALYSIS');
      console.log('='.repeat(80));
      console.log(`Document ID: ${successDoc._id}`);
      console.log(`Created At: ${successDoc.createdAt}`);
      console.log(`User ID: ${successDoc.userId}`);
      console.log(`Model: ${successDoc.model}`);
      console.log(`Is Error: ${successDoc.isError}`);
      console.log(`Output Results Type: ${typeof successDoc.outputResults}`);
      console.log(`Output Results Keys: ${Object.keys(successDoc.outputResults || {}).join(', ')}`);
      
      // Parse and analyze the raw output
      let successParsedOutput = null;
      try {
        const successOutputData = JSON.parse(successDoc.output);
        successParsedOutput = successOutputData.output;
        console.log(`Raw Output Structure: ${JSON.stringify(successOutputData, null, 2).substring(0, 500)}...`);
      } catch (e) {
        console.log(`Raw Output (unparseable): ${successDoc.output.substring(0, 500)}...`);
      }
      
      console.log('\n' + '='.repeat(80));
      console.log('FAILED DOCUMENT ANALYSIS');
      console.log('='.repeat(80));
      console.log(`Document ID: ${failedDoc._id}`);
      console.log(`Created At: ${failedDoc.createdAt}`);
      console.log(`User ID: ${failedDoc.userId}`);
      console.log(`Model: ${failedDoc.model}`);
      console.log(`Is Error: ${failedDoc.isError}`);
      console.log(`Output Results Type: ${typeof failedDoc.outputResults}`);
      console.log(`Output Results: ${JSON.stringify(failedDoc.outputResults)}`);
      
      // Parse and analyze the raw output
      let failedParsedOutput = null;
      try {
        const failedOutputData = JSON.parse(failedDoc.output);
        failedParsedOutput = failedOutputData.output;
        console.log(`Raw Output Structure: ${JSON.stringify(failedOutputData, null, 2).substring(0, 500)}...`);
      } catch (e) {
        console.log(`Raw Output (unparseable): ${failedDoc.output.substring(0, 500)}...`);
      }
      
      console.log('\n' + '='.repeat(80));
      console.log('REVERSE ENGINEERING - PROCESSING FAILED OUTPUT');
      console.log('='.repeat(80));

      // Process the failed document through our simulation
      if (failedDoc.output) {
        let rawOpenAIResponse;
        try {
          const outputData = JSON.parse(failedDoc.output);
          rawOpenAIResponse = outputData.output || failedDoc.output;
        } catch (e) {
          rawOpenAIResponse = failedDoc.output;
        }

        console.log('🔄 Processing FAILED document through simulation...');
        const failedResult = simulateProcessing(rawOpenAIResponse, 0, 'en');

        console.log('\n' + '='.repeat(80));
        console.log('PROCESSING SUCCESS OUTPUT FOR COMPARISON');
        console.log('='.repeat(80));

        let successRawResponse;
        try {
          const outputData = JSON.parse(successDoc.output);
          successRawResponse = outputData.output || successDoc.output;
        } catch (e) {
          successRawResponse = successDoc.output;
        }

        console.log('🔄 Processing SUCCESS document through simulation...');
        const successResult = simulateProcessing(successRawResponse, 0, 'en');
      }
      
      console.log('\n' + '='.repeat(80));
      console.log('POTENTIAL ISSUES IDENTIFIED');
      console.log('='.repeat(80));
      
      // Compare the structures and identify potential issues
      const issues = [];
      
      if (successParsedOutput && failedParsedOutput) {
        // Compare structure
        const successKeys = Object.keys(successParsedOutput);
        const failedKeys = Object.keys(failedParsedOutput);
        
        if (JSON.stringify(successKeys.sort()) !== JSON.stringify(failedKeys.sort())) {
          issues.push(`Different top-level keys: Success has [${successKeys.join(', ')}], Failed has [${failedKeys.join(', ')}]`);
        }
        
        // Check for the specific nested structure
        const hasSuccessNestedStructure = successParsedOutput.insights_overall && 
                                        successParsedOutput.insights_overall.insights_biography && 
                                        successParsedOutput.insights_overall.insights_photos &&
                                        successParsedOutput.insights_overall.insights_prompts;
                                        
        const hasFailedNestedStructure = failedParsedOutput.insights_overall && 
                                       failedParsedOutput.insights_overall.insights_biography && 
                                       failedParsedOutput.insights_overall.insights_photos &&
                                       failedParsedOutput.insights_overall.insights_prompts;
        
        if (hasSuccessNestedStructure !== hasFailedNestedStructure) {
          issues.push(`Nested structure mismatch: Success has nested structure: ${hasSuccessNestedStructure}, Failed has nested structure: ${hasFailedNestedStructure}`);
        }
      }
      
      issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
      
      if (issues.length === 0) {
        console.log('🤔 No obvious structural differences found. The issue might be more subtle.');
      }
      
    } else {
      console.log('⚠️  Need both success and failed documents to compare. Please ensure you have both types in your database.');
    }
    
  } catch (error) {
    console.error('❌ Error during analysis:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the analysis
analyzeProfileAnalysisIssue();
