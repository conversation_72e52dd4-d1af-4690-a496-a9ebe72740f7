const mongoose = require('mongoose');
const UsersProfileAnalysisPrompts = require('./models/users-profile-analysis-prompts');

// Connect to your local database - UPDATE THIS CONNECTION STRING
mongoose.connect('mongodb://localhost:27017/your_database_name', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function findAndAnalyzeDocuments() {
  try {
    console.log('🔍 Looking for success and failed documents...\n');
    
    // Find one success document (has outputResults with content)
    const successDoc = await UsersProfileAnalysisPrompts.findOne({
      isError: false,
      outputResults: { $exists: true, $ne: null, $ne: {}, $ne: [] }
    }).sort({ createdAt: -1 });
    
    // Find one failed document (has output but empty/missing outputResults)
    const failedDoc = await UsersProfileAnalysisPrompts.findOne({
      isError: false,
      output: { $exists: true, $ne: null },
      $or: [
        { outputResults: { $exists: false } },
        { outputResults: null },
        { outputResults: {} },
        { outputResults: [] }
      ]
    }).sort({ createdAt: -1 });
    
    if (!successDoc) {
      console.log('❌ No success document found. Please ensure you have at least one document with populated outputResults.');
      return;
    }
    
    if (!failedDoc) {
      console.log('❌ No failed document found. Please ensure you have at least one document with empty outputResults but valid output.');
      return;
    }
    
    console.log('✅ Found both success and failed documents\n');
    
    console.log('='.repeat(80));
    console.log('SUCCESS DOCUMENT');
    console.log('='.repeat(80));
    console.log(`ID: ${successDoc._id}`);
    console.log(`Created: ${successDoc.createdAt}`);
    console.log(`User: ${successDoc.userId}`);
    console.log(`Model: ${successDoc.model}`);
    console.log(`OutputResults type: ${typeof successDoc.outputResults}`);
    console.log(`OutputResults keys: ${Object.keys(successDoc.outputResults || {}).join(', ')}`);
    
    // Show the raw output structure
    try {
      const successOutputData = JSON.parse(successDoc.output);
      console.log('\n📋 SUCCESS Raw Output Structure:');
      console.log(JSON.stringify(successOutputData, null, 2));
    } catch (e) {
      console.log('\n📋 SUCCESS Raw Output (not JSON):');
      console.log(successDoc.output);
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('FAILED DOCUMENT');
    console.log('='.repeat(80));
    console.log(`ID: ${failedDoc._id}`);
    console.log(`Created: ${failedDoc.createdAt}`);
    console.log(`User: ${failedDoc.userId}`);
    console.log(`Model: ${failedDoc.model}`);
    console.log(`OutputResults: ${JSON.stringify(failedDoc.outputResults)}`);
    
    // Show the raw output structure
    try {
      const failedOutputData = JSON.parse(failedDoc.output);
      console.log('\n📋 FAILED Raw Output Structure:');
      console.log(JSON.stringify(failedOutputData, null, 2));
    } catch (e) {
      console.log('\n📋 FAILED Raw Output (not JSON):');
      console.log(failedDoc.output);
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('ANALYSIS SUMMARY');
    console.log('='.repeat(80));
    
    // Extract the actual OpenAI responses for comparison
    let successOpenAIResponse, failedOpenAIResponse;
    
    try {
      const successData = JSON.parse(successDoc.output);
      successOpenAIResponse = successData.output;
    } catch (e) {
      successOpenAIResponse = successDoc.output;
    }
    
    try {
      const failedData = JSON.parse(failedDoc.output);
      failedOpenAIResponse = failedData.output;
    } catch (e) {
      failedOpenAIResponse = failedDoc.output;
    }
    
    console.log('🔍 Key Differences to Investigate:');
    console.log(`1. Success outputResults has ${Object.keys(successDoc.outputResults || {}).length} keys`);
    console.log(`2. Failed outputResults is: ${JSON.stringify(failedDoc.outputResults)}`);
    
    // Try to parse both OpenAI responses to see the structure
    try {
      const successParsed = JSON.parse(successOpenAIResponse.replace('```json', '').replace('```', '').replace(/\n/g, '').trim());
      const failedParsed = JSON.parse(failedOpenAIResponse.replace('```json', '').replace('```', '').replace(/\n/g, '').trim());
      
      console.log('\n📊 OpenAI Response Structure Comparison:');
      console.log(`Success response keys: ${Object.keys(successParsed).join(', ')}`);
      console.log(`Failed response keys: ${Object.keys(failedParsed).join(', ')}`);
      
      if (successParsed.output && failedParsed.output) {
        console.log(`Success output keys: ${Object.keys(successParsed.output).join(', ')}`);
        console.log(`Failed output keys: ${Object.keys(failedParsed.output).join(', ')}`);
        
        // Check the critical nested structure
        const successHasNested = successParsed.output.insights_overall?.insights_biography && 
                                successParsed.output.insights_overall?.insights_photos && 
                                successParsed.output.insights_overall?.insights_prompts;
        const failedHasNested = failedParsed.output.insights_overall?.insights_biography && 
                               failedParsed.output.insights_overall?.insights_photos && 
                               failedParsed.output.insights_overall?.insights_prompts;
        
        console.log(`\n🎯 Critical Check - Nested Structure:`);
        console.log(`Success has nested structure: ${successHasNested}`);
        console.log(`Failed has nested structure: ${failedHasNested}`);
        
        if (successHasNested !== failedHasNested) {
          console.log('\n🚨 POTENTIAL ISSUE FOUND:');
          console.log('The nested structure condition is different between success and failed documents!');
          console.log('This is likely why outputResults is empty for the failed document.');
        }
      }
      
    } catch (parseError) {
      console.log('\n❌ Could not parse one or both OpenAI responses:', parseError.message);
    }
    
  } catch (error) {
    console.error('❌ Error during analysis:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the analysis
findAndAnalyzeDocuments();
